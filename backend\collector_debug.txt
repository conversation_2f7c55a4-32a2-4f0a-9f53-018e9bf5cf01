============================= test session starts =============================
platform win32 -- Python 3.12.9, pytest-8.3.4, pluggy-1.5.0 -- D:\backup\AI\miniconda3\python.exe
cachedir: .pytest_cache
rootdir: D:\backup\AI\project\AI_Nasdaq_Trading\backend
configfile: pytest.ini
plugins: anyio-4.9.0, Faker-37.4.0, asyncio-0.24.0, cov-6.0.0, mock-3.14.0
asyncio: mode=Mode.AUTO, default_loop_scope=function
collecting ... collected 1 item

tests/test_data_collector.py::TestMockDataCollector::test_initialization FAILED [100%]

================================== FAILURES ===================================
__________________ TestMockDataCollector.test_initialization __________________

self = <tests.test_data_collector.TestMockDataCollector object at 0x000001728A86BB60>
collector = <app.data_pipeline.collector.MockDataCollector object at 0x000001728A8E9D30>

    def test_initialization(self, collector):
        """测试初始化"""
        assert collector.symbols == ["159509"]
        assert collector.collection_interval == 0.1
        assert not collector.is_running
>       assert collector.collected_count == 0
E       AttributeError: 'MockDataCollector' object has no attribute 'collected_count'

tests\test_data_collector.py:45: AttributeError
=========================== short test summary info ===========================
FAILED tests/test_data_collector.py::TestMockDataCollector::test_initialization - AttributeError: 'MockDataCollector' object has no attribute 'collected_count'
============================== 1 failed in 0.63s ==============================
