"""
海天AI纳斯达克交易系统 - 数据分发器模块
基于: 项目手册4.3节数据处理管道设计
创建日期: 2025年8月1日
技术栈: FastAPI 0.116.1 + Python 3.13.2
功能: 将处理后的数据推送给AI交易员，支持WebSocket实时推送
"""

import asyncio
import json
import logging
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Dict, List, Optional, Any, Set, Callable
from fastapi import WebSocket

from app.schemas.market import MarketData, TechnicalIndicator
from app.core.exceptions import BusinessLogicException

logger = logging.getLogger(__name__)


class DataDistributor(ABC):
    """数据分发器抽象基类"""
    
    def __init__(self):
        self.subscribers = {}
        self.distribution_count = 0
        self.error_count = 0
    
    @abstractmethod
    async def distribute(self, data: Any, target: Optional[str] = None) -> bool:
        """分发数据"""
        pass
    
    @abstractmethod
    async def subscribe(self, subscriber_id: str, callback: Callable) -> bool:
        """订阅数据"""
        pass
    
    @abstractmethod
    async def unsubscribe(self, subscriber_id: str) -> bool:
        """取消订阅"""
        pass
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取分发统计信息"""
        return {
            "subscriber_count": len(self.subscribers),
            "distribution_count": self.distribution_count,
            "error_count": self.error_count,
            "error_rate": self.error_count / max(self.distribution_count, 1)
        }


class WebSocketDistributor(DataDistributor):
    """WebSocket数据分发器"""
    
    def __init__(self):
        super().__init__()
        self.websocket_connections: Dict[str, WebSocket] = {}
        self.connection_metadata: Dict[str, Dict[str, Any]] = {}
        self.message_queue: Dict[str, List[Dict[str, Any]]] = {}
        self.max_queue_size = 100

        # 兼容性属性，用于测试
        self.active_connections = self.websocket_connections
        self.total_messages_sent = 0
    
    async def add_websocket_connection(self, connection_id: str, websocket: WebSocket, metadata: Dict[str, Any] = None):
        """添加WebSocket连接"""
        try:
            await websocket.accept()
            self.websocket_connections[connection_id] = websocket
            self.connection_metadata[connection_id] = metadata or {}
            self.message_queue[connection_id] = []
            
            logger.info(f"WebSocket连接已建立: {connection_id}")
            
            # 发送连接确认消息
            await self._send_to_websocket(connection_id, {
                "type": "connection_established",
                "connection_id": connection_id,
                "timestamp": datetime.now().isoformat()
            })
            
        except Exception as e:
            logger.error(f"建立WebSocket连接失败: {connection_id} - {e}")
            raise BusinessLogicException(f"WebSocket连接失败: {e}")

    async def add_connection(self, websocket: WebSocket, connection_id: str = None):
        """兼容性方法：添加连接（用于测试）"""
        if connection_id is None:
            connection_id = f"conn_{len(self.websocket_connections)}"
        await self.add_websocket_connection(connection_id, websocket)

    async def remove_websocket_connection(self, connection_id: str):
        """移除WebSocket连接"""
        if connection_id in self.websocket_connections:
            try:
                websocket = self.websocket_connections[connection_id]
                await websocket.close()
            except Exception as e:
                logger.warning(f"关闭WebSocket连接时出错: {connection_id} - {e}")
            
            # 清理相关数据
            self.websocket_connections.pop(connection_id, None)
            self.connection_metadata.pop(connection_id, None)
            self.message_queue.pop(connection_id, None)
            
            logger.info(f"WebSocket连接已移除: {connection_id}")
    
    async def distribute(self, data: Any, target: Optional[str] = None) -> bool:
        """分发数据到WebSocket连接"""
        try:
            message = await self._prepare_message(data)
            
            if target:
                # 发送给特定目标
                return await self._send_to_websocket(target, message)
            else:
                # 广播给所有连接
                success_count = 0
                for connection_id in list(self.websocket_connections.keys()):
                    if await self._send_to_websocket(connection_id, message):
                        success_count += 1
                
                self.distribution_count += 1
                return success_count > 0
                
        except Exception as e:
            logger.error(f"数据分发失败: {e}")
            self.error_count += 1
            return False
    
    async def subscribe(self, subscriber_id: str, callback: Callable) -> bool:
        """订阅数据（WebSocket不需要回调函数）"""
        # WebSocket连接本身就是订阅
        return subscriber_id in self.websocket_connections
    
    async def unsubscribe(self, subscriber_id: str) -> bool:
        """取消订阅"""
        await self.remove_websocket_connection(subscriber_id)
        return True
    
    async def _prepare_message(self, data: Any) -> Dict[str, Any]:
        """准备消息格式"""
        if isinstance(data, MarketData):
            return {
                "type": "market_data",
                "data": {
                    "symbol": data.symbol,
                    "current_price": float(data.current_price),
                    "open_price": float(data.open_price) if data.open_price else None,
                    "high_price": float(data.high_price) if data.high_price else None,
                    "low_price": float(data.low_price) if data.low_price else None,
                    "volume": data.volume,
                    "turnover": data.turnover,
                    "change": float(data.change) if data.change else None,
                    "change_pct": data.change_pct,
                    "bid_price": float(data.bid_price) if data.bid_price else None,
                    "ask_price": float(data.ask_price) if data.ask_price else None,
                    "bid_volume": data.bid_volume,
                    "ask_volume": data.ask_volume,
                    "timestamp": data.timestamp.isoformat() if data.timestamp else None
                },
                "timestamp": datetime.now().isoformat()
            }
        elif isinstance(data, TechnicalIndicator):
            return {
                "type": "technical_indicator",
                "data": {
                    "symbol": data.symbol,
                    "indicator_name": data.indicator_name,
                    "indicator_value": float(data.indicator_value),
                    "period": data.period,
                    "additional_data": data.additional_data,
                    "timestamp": data.timestamp.isoformat() if data.timestamp else None
                },
                "timestamp": datetime.now().isoformat()
            }
        else:
            return {
                "type": "generic_data",
                "data": data,
                "timestamp": datetime.now().isoformat()
            }
    
    async def _send_to_websocket(self, connection_id: str, message: Dict[str, Any]) -> bool:
        """发送消息到特定WebSocket连接"""
        if connection_id not in self.websocket_connections:
            return False
        
        websocket = self.websocket_connections[connection_id]
        
        try:
            # 检查连接是否仍然活跃
            if websocket.client_state.name != "CONNECTED":
                await self.remove_websocket_connection(connection_id)
                return False
            
            # 发送消息
            await websocket.send_text(json.dumps(message, ensure_ascii=False))
            return True
            
        except Exception as e:
            logger.error(f"发送WebSocket消息失败: {connection_id} - {e}")
            await self.remove_websocket_connection(connection_id)
            return False
    
    async def broadcast_system_message(self, message: str, level: str = "info"):
        """广播系统消息"""
        system_message = {
            "type": "system_message",
            "data": {
                "message": message,
                "level": level,
                "timestamp": datetime.now().isoformat()
            }
        }
        
        await self.distribute(system_message)
    
    async def send_heartbeat(self):
        """发送心跳消息"""
        heartbeat_message = {
            "type": "heartbeat",
            "timestamp": datetime.now().isoformat()
        }
        
        for connection_id in list(self.websocket_connections.keys()):
            await self._send_to_websocket(connection_id, heartbeat_message)
    
    def get_connection_info(self) -> Dict[str, Any]:
        """获取连接信息"""
        return {
            "total_connections": len(self.websocket_connections),
            "connections": [
                {
                    "connection_id": conn_id,
                    "metadata": self.connection_metadata.get(conn_id, {}),
                    "queue_size": len(self.message_queue.get(conn_id, []))
                }
                for conn_id in self.websocket_connections.keys()
            ]
        }

    def get_statistics(self) -> Dict[str, Any]:
        """获取WebSocket分发统计信息"""
        base_stats = super().get_statistics()
        base_stats.update({
            "active_connections": len(self.websocket_connections)
        })
        return base_stats


class AITraderDistributor(DataDistributor):
    """AI交易员数据分发器"""
    
    def __init__(self):
        super().__init__()
        self.ai_trader_callbacks: Dict[str, Callable] = {}
        self.trader_filters: Dict[str, Dict[str, Any]] = {}
        # 添加测试期望的统计字段
        self.total_messages_sent = 0
        self.failed_messages = 0
    
    async def distribute(self, data: Any, target: Optional[str] = None) -> bool:
        """分发数据给AI交易员"""
        try:
            if target:
                # 发送给特定AI交易员
                if target in self.ai_trader_callbacks:
                    callback = self.ai_trader_callbacks[target]
                    await callback(data)
                    self.distribution_count += 1
                    self.total_messages_sent += 1
                    return True
                return False
            else:
                # 发送给所有AI交易员
                success_count = 0
                for trader_id, callback in self.ai_trader_callbacks.items():
                    try:
                        # 检查过滤条件
                        if await self._should_send_to_trader(trader_id, data):
                            await callback(data)
                            success_count += 1
                            self.total_messages_sent += 1
                    except Exception as e:
                        logger.error(f"发送数据给AI交易员失败: {trader_id} - {e}")
                        self.error_count += 1
                        self.failed_messages += 1
                
                self.distribution_count += 1
                return success_count > 0
                
        except Exception as e:
            logger.error(f"AI交易员数据分发失败: {e}")
            self.error_count += 1
            return False
    
    async def subscribe(self, subscriber_id: str, callback: Callable) -> bool:
        """AI交易员订阅数据"""
        self.ai_trader_callbacks[subscriber_id] = callback
        logger.info(f"AI交易员已订阅数据: {subscriber_id}")
        return True
    
    async def unsubscribe(self, subscriber_id: str) -> bool:
        """AI交易员取消订阅"""
        if subscriber_id in self.ai_trader_callbacks:
            del self.ai_trader_callbacks[subscriber_id]
            self.trader_filters.pop(subscriber_id, None)
            logger.info(f"AI交易员已取消订阅: {subscriber_id}")
            return True
        return False
    
    async def set_trader_filter(self, trader_id: str, filters: Dict[str, Any]):
        """设置AI交易员数据过滤条件"""
        self.trader_filters[trader_id] = filters
    
    async def _should_send_to_trader(self, trader_id: str, data: Any) -> bool:
        """检查是否应该发送数据给特定AI交易员"""
        filters = self.trader_filters.get(trader_id, {})
        
        if not filters:
            return True
        
        # 检查标的过滤
        if "symbols" in filters and hasattr(data, "symbol"):
            if data.symbol not in filters["symbols"]:
                return False
        
        # 检查数据类型过滤
        if "data_types" in filters:
            data_type = type(data).__name__
            if data_type not in filters["data_types"]:
                return False
        
        return True

    def get_statistics(self) -> Dict[str, Any]:
        """获取AI交易员分发统计信息"""
        base_stats = super().get_statistics()
        base_stats.update({
            "total_messages_sent": self.total_messages_sent,
            "failed_messages": self.failed_messages,
            "active_traders": len(self.ai_trader_callbacks)
        })
        return base_stats
